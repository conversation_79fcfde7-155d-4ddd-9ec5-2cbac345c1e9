<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI聊天助手</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background: #f5f5f5;
      }
      .chat-container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .chat-header {
        background: #007bff;
        color: white;
        padding: 20px;
        text-align: center;
      }
      .chat-messages {
        height: 400px;
        overflow-y: auto;
        padding: 20px;
      }
      .message {
        margin-bottom: 15px;
      }
      .user-message {
        text-align: right;
      }
      .bot-message {
        text-align: left;
      }
      .message-content {
        display: inline-block;
        max-width: 70%;
        padding: 10px 15px;
        border-radius: 15px;
      }
      .user-message .message-content {
        background: #007bff;
        color: white;
      }
      .bot-message .message-content {
        background: #e9ecef;
        color: #333;
      }
      .chat-input {
        padding: 20px;
        border-top: 1px solid #ddd;
      }
      .input-group {
        display: flex;
        gap: 10px;
      }
      #messageInput {
        flex: 1;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        resize: none;
      }
      #sendBtn {
        padding: 10px 20px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
      }
      #sendBtn:disabled {
        background: #ccc;
        cursor: not-allowed;
      }
      .file-section {
        margin-bottom: 10px;
      }
      #uploadBtn {
        padding: 5px 10px;
        background: #28a745;
        color: white;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        margin-right: 10px;
      }
      .uploaded-files {
        display: inline-block;
      }
      .file-tag {
        display: inline-block;
        background: #17a2b8;
        color: white;
        padding: 2px 8px;
        border-radius: 10px;
        margin-right: 5px;
        font-size: 12px;
      }
      .remove-file {
        cursor: pointer;
        margin-left: 5px;
        font-weight: bold;
      }
      .loading {
        text-align: center;
        color: #666;
        font-style: italic;
      }
      .char-count {
        font-size: 12px;
        color: #666;
        text-align: right;
        margin-top: 5px;
      }
    </style>
  </head>
  <body>
    <div class="chat-container">
      <div class="chat-header">
        <h1>AI聊天助手</h1>
      </div>

      <div class="chat-messages" id="chatMessages">
        <div class="message bot-message">
          <div class="message-content">
            你好！我是AI助手，有什么可以帮助你的吗？
          </div>
        </div>
      </div>

      <div class="chat-input">
        <div class="file-section">
          <input
            type="file"
            id="fileInput"
            accept=".pdf,.txt,.doc,.docx"
            style="display: none"
          />
          <button id="uploadBtn">📎 上传文件</button>
          <div id="uploadedFiles" class="uploaded-files"></div>
        </div>

        <div class="input-group">
          <textarea
            id="messageInput"
            placeholder="输入你的消息..."
            rows="2"
            maxlength="2000"
          ></textarea>
          <button id="sendBtn">发送</button>
        </div>

        <div class="char-count">0/2000</div>
      </div>
    </div>

    <script>
      // 全局变量
      let chatId =
        "chat_" +
        Date.now() +
        "_" +
        Math.random().toString(36).substring(2, 11);
      let uploadedFiles = [];

      // DOM元素
      let chatMessages,
        messageInput,
        sendBtn,
        fileInput,
        uploadBtn,
        uploadedFilesDiv,
        charCount;

      // 初始化
      document.addEventListener("DOMContentLoaded", function () {
        chatMessages = document.getElementById("chatMessages");
        messageInput = document.getElementById("messageInput");
        sendBtn = document.getElementById("sendBtn");
        fileInput = document.getElementById("fileInput");
        uploadBtn = document.getElementById("uploadBtn");
        uploadedFilesDiv = document.getElementById("uploadedFiles");
        charCount = document.querySelector(".char-count");

        // 绑定事件
        sendBtn.addEventListener("click", sendMessage);
        messageInput.addEventListener("keypress", function (e) {
          if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
          }
        });
        messageInput.addEventListener("input", updateCharCount);
        uploadBtn.addEventListener("click", function () {
          fileInput.click();
        });
        fileInput.addEventListener("change", handleFileUpload);

        updateCharCount();
      });

      function updateCharCount() {
        const count = messageInput.value.length;
        charCount.textContent = count + "/2000";
        charCount.style.color = count > 1800 ? "#ff4757" : "#666";
      }

      // 发送消息
      async function sendMessage() {
        const message = messageInput.value.trim();
        if (!message) return;

        // 显示用户消息
        addMessage(message, "user");
        messageInput.value = "";
        updateCharCount();

        try {
          showLoading();
          const response = await callChatAPI(message);
          addMessage(response, "bot");
        } catch (error) {
          console.error("发送消息失败:", error);
          addMessage(
            "抱歉，发生了错误，请稍后重试。错误信息: " + error.message,
            "bot"
          );
        } finally {
          hideLoading();
        }
      }

      async function callChatAPI(message) {
        const annotations = [];
        if (uploadedFiles.length > 0) {
          annotations.push({
            type: "document_file",
            data: {
              files: uploadedFiles,
            },
          });
        }

        const requestBody = {
          id: chatId,
          messages: [
            {
              role: "user",
              content: message,
              annotations: annotations,
            },
          ],
          data: {},
        };

        console.log("发送请求:", JSON.stringify(requestBody, null, 2));

        const response = await fetch("/api/chat", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(
            "API调用失败: " + response.status + " - " + errorText
          );
        }

        // 处理流式响应
        const responseText = await response.text();
        console.log("原始响应:", responseText);

        // 解析流式数据
        return parseStreamResponse(responseText);
      }

      function parseStreamResponse(responseText) {
        try {
          console.log("原始响应:", responseText);

          // 分割每一行数据
          const lines = responseText.split("\n").filter((line) => line.trim());
          let finalText = "";
          let toolOutputs = [];

          for (const line of lines) {
            // 移除行号前缀（如 "8:"）
            const cleanLine = line.replace(/^\d+:/, "");

            if (cleanLine.trim()) {
              try {
                const data = JSON.parse(cleanLine);
                console.log("解析的数据:", data);

                // 处理数组格式的数据
                if (Array.isArray(data)) {
                  for (const item of data) {
                    // 提取工具输出 - 这是最重要的内容
                    if (item.tool_output && item.tool_output.content) {
                      toolOutputs.push(item.tool_output.content);
                      console.log("找到工具输出:", item.tool_output.content);
                    }

                    // 提取响应文本块
                    if (item.response && item.response.blocks) {
                      for (const block of item.response.blocks) {
                        if (block.block_type === "text" && block.text) {
                          finalText += block.text;
                        }
                      }
                    }
                  }
                }
              } catch (e) {
                console.log("JSON解析失败:", cleanLine);
              }
            }
          }

          // 优先使用工具输出，因为这通常包含实际的答案
          if (toolOutputs.length > 0) {
            finalText = toolOutputs.join("\n\n");
          }

          // 如果仍然没有文本，尝试解码Unicode
          if (!finalText.trim()) {
            // 解码Unicode字符
            let decodedText = responseText.replace(
              /\\u([\da-f]{4})/gi,
              function (match, grp) {
                return String.fromCharCode(parseInt(grp, 16));
              }
            );

            console.log("解码后的文本:", decodedText);

            // 尝试从解码后的文本中提取内容
            const contentMatches = decodedText.match(/"content":\s*"([^"]+)"/g);
            if (contentMatches) {
              for (const match of contentMatches) {
                const content = match.match(/"content":\s*"([^"]+)"/)[1];
                if (
                  content &&
                  content.trim() &&
                  !content.includes("Calling tool")
                ) {
                  finalText += content + "\n";
                }
              }
            }
          }

          return finalText.trim() || "抱歉，AI没有返回有效的回复。";
        } catch (error) {
          console.error("解析响应失败:", error);
          return "抱歉，解析AI回复时出现错误: " + error.message;
        }
      }

      // 文件上传处理
      async function handleFileUpload(event) {
        const files = Array.from(event.target.files);

        for (const file of files) {
          try {
            showLoading("正在上传文件...");
            const uploadedFile = await uploadFile(file);
            uploadedFiles.push(uploadedFile);
            displayUploadedFile(uploadedFile);
            addMessage("文件上传成功: " + file.name, "bot");
          } catch (error) {
            console.error("文件上传失败:", error);
            addMessage("文件上传失败: " + error.message, "bot");
          } finally {
            hideLoading();
          }
        }

        fileInput.value = "";
      }

      async function uploadFile(file) {
        const reader = new FileReader();

        return new Promise((resolve, reject) => {
          reader.onload = async function (e) {
            try {
              const base64 = e.target.result.split(",")[1];

              const response = await fetch("/api/chat/file", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  name: file.name,
                  base64: base64,
                  params: JSON.stringify({
                    size: file.size,
                    type: file.type,
                  }),
                }),
              });

              if (!response.ok) {
                const errorText = await response.text();
                throw new Error(
                  "上传失败: " + response.status + " - " + errorText
                );
              }

              const result = await response.json();
              resolve(result);
            } catch (error) {
              reject(error);
            }
          };

          reader.onerror = function () {
            reject(new Error("文件读取失败"));
          };
          reader.readAsDataURL(file);
        });
      }

      function displayUploadedFile(file) {
        const fileTag = document.createElement("div");
        fileTag.className = "file-tag";
        fileTag.innerHTML =
          "📄 " +
          file.id +
          ' <span class="remove-file" onclick="removeFile(\'' +
          file.id +
          "')\">&times;</span>";
        uploadedFilesDiv.appendChild(fileTag);
      }

      function removeFile(fileId) {
        uploadedFiles = uploadedFiles.filter((f) => f.id !== fileId);
        updateUploadedFilesDisplay();
      }

      function updateUploadedFilesDisplay() {
        uploadedFilesDiv.innerHTML = "";
        uploadedFiles.forEach((file) => displayUploadedFile(file));
      }

      // 添加消息到聊天界面
      function addMessage(text, sender) {
        const messageDiv = document.createElement("div");
        messageDiv.className = "message " + sender + "-message";
        messageDiv.innerHTML =
          '<div class="message-content">' + escapeHtml(text) + "</div>";
        chatMessages.appendChild(messageDiv);
        scrollToBottom();
      }

      function escapeHtml(text) {
        const div = document.createElement("div");
        div.textContent = text;
        return div.innerHTML;
      }

      function scrollToBottom() {
        chatMessages.scrollTop = chatMessages.scrollHeight;
      }

      function showLoading(text) {
        if (!text) text = "AI正在思考中...";
        addMessage(text, "bot");
        sendBtn.disabled = true;
      }

      function hideLoading() {
        sendBtn.disabled = false;
      }
    </script>
  </body>
</html>
