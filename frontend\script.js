class ChatApp {
    constructor() {
        this.chatId = this.generateChatId();
        this.uploadedFiles = [];
        this.initializeElements();
        this.bindEvents();
        this.updateCharCount();
    }

    generateChatId() {
        return 'chat_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    initializeElements() {
        this.chatMessages = document.getElementById('chatMessages');
        this.messageInput = document.getElementById('messageInput');
        this.sendBtn = document.getElementById('sendBtn');
        this.fileInput = document.getElementById('fileInput');
        this.uploadBtn = document.getElementById('uploadBtn');
        this.uploadedFiles = document.getElementById('uploadedFiles');
        this.loadingOverlay = document.getElementById('loadingOverlay');
        this.charCount = document.querySelector('.char-count');
    }

    bindEvents() {
        this.sendBtn.addEventListener('click', () => this.sendMessage());
        this.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        this.messageInput.addEventListener('input', () => {
            this.autoResize();
            this.updateCharCount();
        });
        this.uploadBtn.addEventListener('click', () => this.fileInput.click());
        this.fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
    }

    autoResize() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
    }

    updateCharCount() {
        const count = this.messageInput.value.length;
        this.charCount.textContent = `${count}/2000`;
        this.charCount.style.color = count > 1800 ? '#ff4757' : '#666';
    }

    async handleFileUpload(event) {
        const files = Array.from(event.target.files);
        
        for (const file of files) {
            try {
                this.showLoading('正在上传文件...');
                const uploadedFile = await this.uploadFile(file);
                this.uploadedFiles.push(uploadedFile);
                this.displayUploadedFile(uploadedFile);
            } catch (error) {
                console.error('文件上传失败:', error);
                this.showError('文件上传失败: ' + error.message);
            } finally {
                this.hideLoading();
            }
        }
        
        // 清空文件输入
        this.fileInput.value = '';
    }

    async uploadFile(file) {
        const reader = new FileReader();
        
        return new Promise((resolve, reject) => {
            reader.onload = async (e) => {
                try {
                    const base64 = e.target.result.split(',')[1];
                    
                    const response = await fetch('/api/chat/file', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            name: file.name,
                            base64: base64,
                            params: JSON.stringify({
                                size: file.size,
                                type: file.type
                            })
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`上传失败: ${response.status}`);
                    }

                    const result = await response.json();
                    resolve(result);
                } catch (error) {
                    reject(error);
                }
            };
            
            reader.onerror = () => reject(new Error('文件读取失败'));
            reader.readAsDataURL(file);
        });
    }

    displayUploadedFile(file) {
        const fileTag = document.createElement('div');
        fileTag.className = 'file-tag';
        fileTag.innerHTML = `
            📄 ${file.id}
            <span class="remove-file" onclick="chatApp.removeFile('${file.id}')">&times;</span>
        `;
        this.uploadedFiles.appendChild(fileTag);
    }

    removeFile(fileId) {
        this.uploadedFiles = this.uploadedFiles.filter(f => f.id !== fileId);
        this.updateUploadedFilesDisplay();
    }

    updateUploadedFilesDisplay() {
        this.uploadedFiles.innerHTML = '';
        this.uploadedFiles.forEach(file => this.displayUploadedFile(file));
    }

    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message) return;

        // 显示用户消息
        this.addMessage(message, 'user');
        this.messageInput.value = '';
        this.autoResize();
        this.updateCharCount();

        try {
            this.showLoading();
            const response = await this.callChatAPI(message);
            this.addMessage(response, 'bot');
        } catch (error) {
            console.error('发送消息失败:', error);
            this.addMessage('抱歉，发生了错误，请稍后重试。', 'bot');
        } finally {
            this.hideLoading();
        }
    }

    async callChatAPI(message) {
        const requestBody = {
            id: this.chatId,
            messages: [
                {
                    role: "user",
                    content: message,
                    annotations: this.uploadedFiles.map(file => ({
                        type: "document_file",
                        data: {
                            files: [file]
                        }
                    }))
                }
            ],
            data: {}
        };

        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            throw new Error(`API调用失败: ${response.status}`);
        }

        return await response.text();
    }

    addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        
        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });

        messageDiv.innerHTML = `
            <div class="message-content">
                <div class="message-text">${this.escapeHtml(text)}</div>
                <div class="message-time">${timeString}</div>
            </div>
        `;

        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    showLoading(text = 'AI正在思考中...') {
        this.loadingOverlay.querySelector('.loading-text').textContent = text;
        this.loadingOverlay.style.display = 'flex';
        this.sendBtn.disabled = true;
    }

    hideLoading() {
        this.loadingOverlay.style.display = 'none';
        this.sendBtn.disabled = false;
    }

    showError(message) {
        this.addMessage(`错误: ${message}`, 'bot');
    }
}

// 初始化聊天应用
let chatApp;
document.addEventListener('DOMContentLoaded', () => {
    chatApp = new ChatApp();
});
