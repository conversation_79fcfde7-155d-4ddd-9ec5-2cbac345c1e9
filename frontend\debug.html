<!DOCTYPE html>
<html>
  <head>
    <title>调试聊天API</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
      }
      .container {
        max-width: 800px;
        margin: 0 auto;
      }
      textarea {
        width: 100%;
        height: 100px;
        margin: 10px 0;
      }
      button {
        padding: 10px 20px;
        margin: 5px;
      }
      .response {
        background: #f0f0f0;
        padding: 15px;
        margin: 10px 0;
        border-radius: 5px;
      }
      .raw-response {
        background: #ffe6e6;
        padding: 10px;
        margin: 10px 0;
        border-radius: 5px;
        font-family: monospace;
        white-space: pre-wrap;
      }
      .parsed-response {
        background: #e6ffe6;
        padding: 10px;
        margin: 10px 0;
        border-radius: 5px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>调试聊天API</h1>

      <textarea id="messageInput" placeholder="输入消息">
存货盘点差异的定义</textarea
      >
      <br />
      <button onclick="testChat()">发送消息</button>
      <button onclick="clearResults()">清空结果</button>

      <div id="results"></div>
    </div>

    <script>
      let chatId = "debug_" + Date.now();

      function clearResults() {
        document.getElementById("results").innerHTML = "";
      }

      async function testChat() {
        const message = document.getElementById("messageInput").value.trim();
        if (!message) return;

        const resultsDiv = document.getElementById("results");

        // 显示发送的消息
        const messageDiv = document.createElement("div");
        messageDiv.className = "response";
        messageDiv.innerHTML = "<h3>发送的消息:</h3>" + message;
        resultsDiv.appendChild(messageDiv);

        try {
          const requestBody = {
            id: chatId,
            messages: [
              {
                role: "user",
                content: message,
                annotations: [],
              },
            ],
            data: {},
          };

          console.log("发送请求:", requestBody);

          const response = await fetch("/api/chat", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(requestBody),
          });

          if (!response.ok) {
            throw new Error(
              "HTTP " + response.status + ": " + (await response.text())
            );
          }

          const responseText = await response.text();

          // 显示原始响应
          const rawDiv = document.createElement("div");
          rawDiv.className = "raw-response";
          rawDiv.innerHTML = "<h3>原始响应:</h3>" + responseText;
          resultsDiv.appendChild(rawDiv);

          // 解析并显示处理后的响应
          const parsedText = parseStreamResponse(responseText);
          const parsedDiv = document.createElement("div");
          parsedDiv.className = "parsed-response";
          parsedDiv.innerHTML = "<h3>解析后的文本:</h3>" + parsedText;
          resultsDiv.appendChild(parsedDiv);
        } catch (error) {
          const errorDiv = document.createElement("div");
          errorDiv.className = "response";
          errorDiv.style.background = "#ffcccc";
          errorDiv.innerHTML = "<h3>错误:</h3>" + error.message;
          resultsDiv.appendChild(errorDiv);
        }
      }

      function parseStreamResponse(responseText) {
        try {
          console.log("开始解析响应:", responseText);

          // 分割每一行数据
          const lines = responseText.split("\n").filter((line) => line.trim());
          let finalText = "";
          let toolOutputs = [];
          let debugInfo = [];

          for (const line of lines) {
            // 移除行号前缀（如 "8:"）
            const cleanLine = line.replace(/^\d+:/, "");

            if (cleanLine.trim()) {
              try {
                const data = JSON.parse(cleanLine);
                debugInfo.push(
                  "解析JSON成功: " + JSON.stringify(data, null, 2)
                );

                // 处理不同类型的数据
                if (Array.isArray(data)) {
                  for (const item of data) {
                    // 优先提取工具输出 - 这通常是最终答案
                    if (item.tool_output && item.tool_output.content) {
                      toolOutputs.push(item.tool_output.content);
                      debugInfo.push(
                        "★ 找到工具输出: " +
                          item.tool_output.content.substring(0, 200) +
                          "..."
                      );
                    }

                    // 检查响应块
                    if (item.response && item.response.blocks) {
                      debugInfo.push(
                        "找到响应块: " + item.response.blocks.length + " 个"
                      );
                      for (const block of item.response.blocks) {
                        if (block.block_type === "text" && block.text) {
                          finalText += block.text;
                          debugInfo.push("提取响应文本: " + block.text);
                        }
                      }
                    }

                    // 检查其他可能的文本字段
                    if (item.type === "agent" && item.data && item.data.text) {
                      debugInfo.push("代理文本: " + item.data.text);
                    }
                  }
                }
              } catch (e) {
                debugInfo.push("JSON解析失败: " + cleanLine);
              }
            }
          }

          // 优先使用工具输出
          if (toolOutputs.length > 0) {
            finalText = toolOutputs.join("\n\n");
            debugInfo.push("★ 使用工具输出作为最终结果");
          }

          console.log("调试信息:", debugInfo);

          // 如果没有提取到文本，尝试其他方法
          if (!finalText.trim()) {
            // 解码Unicode字符
            let decodedText = responseText.replace(
              /\\u([\da-f]{4})/gi,
              function (match, grp) {
                return String.fromCharCode(parseInt(grp, 16));
              }
            );

            debugInfo.push(
              "解码后的文本: " + decodedText.substring(0, 500) + "..."
            );

            // 尝试从解码后的文本中提取内容
            const contentMatches = decodedText.match(/"content":\s*"([^"]+)"/g);
            if (contentMatches) {
              for (const match of contentMatches) {
                const content = match.match(/"content":\s*"([^"]+)"/)[1];
                if (
                  content &&
                  content.trim() &&
                  !content.includes("Calling tool")
                ) {
                  finalText += content + "\n";
                  debugInfo.push("从content字段提取: " + content);
                }
              }
            }

            // 最后的备选方案
            if (!finalText.trim()) {
              finalText = "调试信息:\n" + debugInfo.join("\n");
            }
          }

          return finalText || "无法提取有效文本";
        } catch (error) {
          console.error("解析响应失败:", error);
          return "解析错误: " + error.message;
        }
      }
    </script>
  </body>
</html>
